<?php
/**
 * Plugin Name: CFB Price Calculator
 * Plugin URI: https://yourwebsite.com/cfb-calculator
 * Description: A beautiful and powerful price calculation form builder with conditional logic, complex formulas, and multi-language support.
 * Version: 1.0.7
 * Author: CFB Team
 * Text Domain: cfb-calculator
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('CFB_CALCULATOR_VERSION', '1.0.7');
define('CFB_CALCULATOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('CFB_CALCULATOR_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('CFB_CALCULATOR_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main CFB Calculator Class
 */
class CFB_Calculator {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'load_textdomain'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_cfb_save_form', array($this, 'ajax_save_form'));
        add_action('wp_ajax_cfb_calculate_price', array($this, 'ajax_calculate_price'));
        add_action('wp_ajax_nopriv_cfb_calculate_price', array($this, 'ajax_calculate_price'));
        add_action('wp_ajax_cfb_delete_form', array($this, 'ajax_delete_form'));
        add_action('wp_ajax_cfb_duplicate_form', array($this, 'ajax_duplicate_form'));
        add_action('wp_ajax_cfb_toggle_form_status', array($this, 'ajax_toggle_form_status'));
        
        // Admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Shortcode
        add_shortcode('cfb_calculator', array($this, 'render_calculator_shortcode'));
        
        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-database.php';
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-form-builder.php';
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-formula-engine.php';
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-frontend.php';
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-admin.php';
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-settings.php';
        require_once CFB_CALCULATOR_PLUGIN_PATH . 'includes/class-cfb-variables.php';

        // Initialize classes
        CFB_Database::get_instance();
        CFB_Form_Builder::get_instance();
        CFB_Formula_Engine::get_instance();
        CFB_Frontend::get_instance();
        CFB_Admin::get_instance();
        CFB_Settings::get_instance();
        CFB_Variables::get_instance();

        // Ensure tables exist on every load (for debugging)
        add_action('admin_init', array($this, 'ensure_tables_exist'));

        // Run migrations on admin init
        add_action('admin_init', array($this, 'run_migrations'));
    }
    
    /**
     * Ensure tables exist (for debugging)
     */
    public function ensure_tables_exist() {
        global $wpdb;

        $variables_table = $wpdb->prefix . 'cfb_variables';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$variables_table'");

        if (!$table_exists) {
            error_log('CFB: Variables table missing, creating...');
            CFB_Database::get_instance()->create_tables();
        }
    }

    /**
     * Run database migrations
     */
    public function run_migrations() {
        // Only run migrations once per version
        $last_migration_version = get_option('cfb_last_migration_version', '0.0.0');

        if (version_compare($last_migration_version, CFB_CALCULATOR_VERSION, '<')) {
            CFB_Database::get_instance()->create_tables(); // This will run migrations
            update_option('cfb_last_migration_version', CFB_CALCULATOR_VERSION);
            error_log('CFB: Migrations completed for version ' . CFB_CALCULATOR_VERSION);
        }
    }

    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain('cfb-calculator', false, dirname(CFB_CALCULATOR_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style('cfb-calculator-frontend', CFB_CALCULATOR_PLUGIN_URL . 'assets/css/frontend.css', array(), CFB_CALCULATOR_VERSION);
        wp_enqueue_script('cfb-calculator-frontend', CFB_CALCULATOR_PLUGIN_URL . 'assets/js/frontend.js', array('jquery'), CFB_CALCULATOR_VERSION, true);
        
        // Localize script for AJAX
        wp_localize_script('cfb-calculator-frontend', 'cfb_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cfb_calculator_nonce'),
            'currency_position' => get_option('cfb_currency_position', 'left'),
            'currency_symbol' => get_option('cfb_currency_symbol', '$'),
            'decimal_places' => get_option('cfb_decimal_places', 2),
            'thousand_separator' => get_option('cfb_thousand_separator', ','),
            'decimal_separator' => get_option('cfb_decimal_separator', '.'),
            'auto_calculate' => get_option('cfb_auto_calculate', 1),
            'rtl_mode' => is_rtl()
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'cfb-calculator') === false) {
            return;
        }

        wp_enqueue_style('cfb-calculator-admin', CFB_CALCULATOR_PLUGIN_URL . 'assets/css/admin.css', array(), CFB_CALCULATOR_VERSION);
        wp_enqueue_script('cfb-formula-builder', CFB_CALCULATOR_PLUGIN_URL . 'assets/js/formula-builder.js', array('jquery'), CFB_CALCULATOR_VERSION, true);
        wp_enqueue_script('cfb-field-formula-builder', CFB_CALCULATOR_PLUGIN_URL . 'assets/js/field-formula-builder.js', array('jquery'), CFB_CALCULATOR_VERSION, true);
        wp_enqueue_script('cfb-calculator-admin', CFB_CALCULATOR_PLUGIN_URL . 'assets/js/admin.js', array('jquery', 'jquery-ui-sortable', 'jquery-ui-draggable', 'cfb-formula-builder', 'cfb-field-formula-builder'), CFB_CALCULATOR_VERSION, true);

        // Enqueue variables script on variables page
        if (isset($_GET['page']) && $_GET['page'] === 'cfb-calculator-variables') {
            wp_enqueue_script(
                'cfb-variables-js',
                CFB_CALCULATOR_PLUGIN_URL . 'assets/js/variables.js',
                array('jquery'),
                CFB_CALCULATOR_VERSION,
                true
            );
        }

        // Get variables for formula builder
        $variables = array();
        if (class_exists('CFB_Database')) {
            $db_variables = CFB_Database::get_instance()->get_variables();
            foreach ($db_variables as $var) {
                $variables[] = array(
                    'name' => $var->name,
                    'label' => $var->label,
                    'value' => number_format($var->value, 2),
                    'description' => $var->description,
                    'icon' => $var->icon,
                    'color' => $var->color
                );
            }
        }

        wp_localize_script('cfb-calculator-admin', 'cfb_admin_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cfb_admin_nonce')
        ));

        // Pass variables data to JavaScript
        wp_localize_script('cfb-calculator-admin', 'cfbVariables', $variables);

        // Pass admin URLs
        wp_localize_script('cfb-calculator-admin', 'cfbAdminUrls', array(
            'variables' => admin_url('admin.php?page=cfb-calculator-variables'),
            'settings' => admin_url('admin.php?page=cfb-calculator-settings'),
            'forms' => admin_url('admin.php?page=cfb-calculator')
        ));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('CFB Calculator', 'cfb-calculator'),
            __('CFB Calculator', 'cfb-calculator'),
            'manage_options',
            'cfb-calculator',
            array($this, 'admin_page'),
            'dashicons-calculator',
            30
        );
        
        add_submenu_page(
            'cfb-calculator',
            __('All Forms', 'cfb-calculator'),
            __('All Forms', 'cfb-calculator'),
            'manage_options',
            'cfb-calculator',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'cfb-calculator',
            __('Add New Form', 'cfb-calculator'),
            __('Add New Form', 'cfb-calculator'),
            'manage_options',
            'cfb-calculator-new',
            array($this, 'admin_new_form_page')
        );

        add_submenu_page(
            'cfb-calculator',
            __('Variables', 'cfb-calculator'),
            __('Variables', 'cfb-calculator'),
            'manage_options',
            'cfb-calculator-variables',
            array($this, 'admin_variables_page')
        );

        add_submenu_page(
            'cfb-calculator',
            __('Settings', 'cfb-calculator'),
            __('Settings', 'cfb-calculator'),
            'manage_options',
            'cfb-calculator-settings',
            array($this, 'admin_settings_page')
        );
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        CFB_Admin::get_instance()->render_forms_list();
    }
    
    /**
     * Admin new form page callback
     */
    public function admin_new_form_page() {
        CFB_Admin::get_instance()->render_form_builder();
    }

    /**
     * Admin variables page callback
     */
    public function admin_variables_page() {
        CFB_Variables::get_instance()->render_variables_page();
    }

    /**
     * Admin settings page callback
     */
    public function admin_settings_page() {
        CFB_Settings::get_instance()->render_settings_page();
    }
    
    /**
     * AJAX save form handler
     */
    public function ajax_save_form() {
        check_ajax_referer('cfb_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Unauthorized access', 'cfb-calculator'));
        }
        
        CFB_Form_Builder::get_instance()->save_form();
    }
    
    /**
     * AJAX calculate price handler
     */
    public function ajax_calculate_price() {
        // Let the formula engine handle its own nonce verification
        CFB_Formula_Engine::get_instance()->calculate_price();
    }

    /**
     * AJAX delete form handler
     */
    public function ajax_delete_form() {
        CFB_Admin::get_instance()->ajax_delete_form();
    }

    /**
     * AJAX duplicate form handler
     */
    public function ajax_duplicate_form() {
        CFB_Admin::get_instance()->ajax_duplicate_form();
    }

    /**
     * AJAX toggle form status handler
     */
    public function ajax_toggle_form_status() {
        CFB_Admin::get_instance()->ajax_toggle_form_status();
    }
    
    /**
     * Render calculator shortcode
     */
    public function render_calculator_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'title' => true
        ), $atts);
        
        return CFB_Frontend::get_instance()->render_calculator($atts['id'], $atts['title']);
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        CFB_Database::get_instance()->create_tables();

        // Set default options
        add_option('cfb_currency_symbol', '$');
        add_option('cfb_currency_position', 'left');
        add_option('cfb_decimal_places', 2);
        add_option('cfb_thousand_separator', ',');
        add_option('cfb_decimal_separator', '.');

        // Force table creation for variables
        $this->force_create_variables_table();

        flush_rewrite_rules();
    }

    /**
     * Force create variables table if missing
     */
    private function force_create_variables_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'cfb_variables';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

        if (!$table_exists) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE $table_name (
                id int(11) NOT NULL AUTO_INCREMENT,
                name varchar(255) NOT NULL,
                label varchar(255) NOT NULL,
                value decimal(20,4) NOT NULL DEFAULT 0,
                description text,
                category varchar(100) DEFAULT 'general',
                icon varchar(50) DEFAULT 'dashicons-admin-settings',
                color varchar(7) DEFAULT '#667eea',
                is_active tinyint(1) DEFAULT 1,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY name (name),
                KEY category (category),
                KEY is_active (is_active)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            error_log('CFB: Variables table creation attempted');
        }
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        flush_rewrite_rules();
    }
}

// Initialize the plugin
function cfb_calculator_init() {
    return CFB_Calculator::get_instance();
}

// Start the plugin
cfb_calculator_init();
