<?php
/**
 * Test Calculation Fix
 * Simple test to verify the count() error is fixed
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>CFB Calculator - Count() Error Fix Test</h1>";

// Test 1: Create a simple form configuration
echo "<h2>1. Testing Simple Form Configuration</h2>";

$test_form_config = array(
    'fields' => array(
        array(
            'type' => 'number',
            'name' => 'quantity',
            'label' => 'Quantity',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        ),
        array(
            'type' => 'calculation',
            'name' => 'total',
            'label' => 'Total',
            'formula' => '{quantity} * 10',
            'display_type' => 'currency',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        )
    )
);

$test_form_data = array(
    'quantity' => 5
);

$test_settings = array(
    'currency_symbol' => '$',
    'currency_position' => 'left',
    'decimal_places' => 2,
    'decimal_separator' => '.',
    'thousand_separator' => ','
);

echo "Form config created with " . count($test_form_config['fields']) . " fields<br>";
echo "Test form data: " . print_r($test_form_data, true) . "<br>";

// Test 2: Test the formula engine
echo "<h2>2. Testing Formula Engine</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "Formula engine instance created successfully<br>";
    
    $result = $formula_engine->process_calculation($test_form_data, $test_form_config, $test_settings);
    echo "Calculation completed successfully!<br>";
    echo "Result: " . print_r($result, true) . "<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

// Test 3: Test with null/empty fields
echo "<h2>3. Testing with Null/Empty Fields</h2>";

$empty_form_config = array(
    'fields' => null
);

try {
    $result = $formula_engine->process_calculation(array(), $empty_form_config, $test_settings);
    echo "Empty fields test passed<br>";
    echo "Result: " . print_r($result, true) . "<br>";
} catch (Exception $e) {
    echo "Error with empty fields: " . $e->getMessage() . "<br>";
}

// Test 4: Test with missing fields array
echo "<h2>4. Testing with Missing Fields Array</h2>";

$missing_fields_config = array();

try {
    $result = $formula_engine->process_calculation(array(), $missing_fields_config, $test_settings);
    echo "Missing fields test passed<br>";
    echo "Result: " . print_r($result, true) . "<br>";
} catch (Exception $e) {
    echo "Error with missing fields: " . $e->getMessage() . "<br>";
}

// Test 5: Test AJAX simulation
echo "<h2>5. Testing AJAX Simulation</h2>";

// Simulate $_POST data
$_POST = array(
    'action' => 'cfb_calculate_price',
    'nonce' => wp_create_nonce('cfb_calculator_nonce'),
    'form_id' => 999, // Fake form ID for testing
    'form_data' => $test_form_data
);

// Create a fake form in database for testing
global $wpdb;
$forms_table = $wpdb->prefix . 'cfb_forms';

// Insert test form
$test_form_data_json = wp_json_encode($test_form_config);
$wpdb->insert(
    $forms_table,
    array(
        'id' => 999,
        'name' => 'Test Form',
        'description' => 'Test form for debugging',
        'form_data' => $test_form_data_json,
        'status' => 'active'
    ),
    array('%d', '%s', '%s', '%s', '%s')
);

echo "Test form inserted into database<br>";

// Test the AJAX handler
try {
    ob_start();
    $formula_engine->calculate_price();
    $output = ob_get_clean();
    echo "AJAX handler output: " . $output . "<br>";
} catch (Exception $e) {
    echo "AJAX Error: " . $e->getMessage() . "<br>";
}

// Clean up test form
$wpdb->delete($forms_table, array('id' => 999), array('%d'));
echo "Test form cleaned up<br>";

echo "<h2>Test Complete!</h2>";
echo "If you see this message without fatal errors, the count() issue has been fixed!";
?>
