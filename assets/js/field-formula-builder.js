/**
 * CFB Field Formula Builder
 * Professional formula builder for individual fields
 */

(function($) {
    'use strict';

    class CFBFieldFormulaBuilder {
        constructor(container, options = {}) {
            this.container = $(container);
            this.options = {
                fields: [],
                fieldType: 'calculation', // calculation or total
                ...options
            };
            
            this.init();
        }

        init() {
            this.render();
            this.bindEvents();
        }

        render() {
            const html = `
                <div class="cfb-field-formula-builder">
                    <div class="cfb-formula-header">
                        <h4>
                            <span class="dashicons dashicons-chart-line"></span>
                            ${this.options.fieldType === 'total' ? 'Total Formula' : 'Calculation Formula'}
                        </h4>
                        <div class="cfb-formula-actions">
                            <button type="button" class="cfb-validate-formula button-secondary">
                                <span class="dashicons dashicons-yes-alt"></span>
                                Validate
                            </button>
                            <button type="button" class="cfb-clear-formula button-secondary">
                                <span class="dashicons dashicons-dismiss"></span>
                                Clear
                            </button>
                        </div>
                    </div>
                    
                    <div class="cfb-formula-workspace">
                        <div class="cfb-formula-editor">
                            <label>Formula:</label>
                            <textarea class="cfb-formula-input" placeholder="Click fields below to build your formula..."></textarea>
                            <div class="cfb-formula-validation"></div>

                            <!-- Operations & Functions Bar Under Formula -->
                            <div class="cfb-operations-bar">
                                <div class="cfb-operations-section">
                                    <h5>
                                        <span class="dashicons dashicons-admin-tools"></span>
                                        Operators
                                    </h5>
                                    <div class="cfb-operators-grid">
                                        ${this.renderOperatorButtons()}
                                    </div>
                                </div>

                                <div class="cfb-operations-section">
                                    <h5>
                                        <span class="dashicons dashicons-calculator"></span>
                                        Functions
                                    </h5>
                                    <div class="cfb-functions-grid">
                                        ${this.renderFunctionButtons()}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="cfb-formula-tools">
                            <div class="cfb-tool-section">
                                <h5>
                                    <span class="dashicons dashicons-list-view"></span>
                                    Available Fields
                                </h5>
                                <div class="cfb-fields-grid">
                                    ${this.renderFieldButtons()}
                                </div>
                            </div>

                            <div class="cfb-tool-section">
                                <h5>
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    Variables
                                </h5>
                                <div class="cfb-variables-grid">
                                    ${this.renderVariableButtons()}
                                </div>
                            </div>

                            ${this.options.fieldType === 'total' ? this.renderCalculationFields() : ''}
                        </div>
                    </div>
                    
                    <div class="cfb-formula-help">
                        <details>
                            <summary>
                                <span class="dashicons dashicons-editor-help"></span>
                                Formula Help & Examples
                            </summary>
                            <div class="cfb-help-content">
                                ${this.renderHelpContent()}
                            </div>
                        </details>
                    </div>
                </div>
            `;
            
            this.container.html(html);
            this.textarea = this.container.find('.cfb-formula-input');
            this.validation = this.container.find('.cfb-formula-validation');
        }

        renderFieldButtons() {
            if (!this.options.fields || this.options.fields.length === 0) {
                return '<p class="cfb-no-fields">No fields available. Add fields to the form first.</p>';
            }

            return this.options.fields.map(field => {
                const icon = this.getFieldIcon(field.type);
                return `
                    <button type="button" class="cfb-field-btn" data-field="${field.name}" title="Add ${field.label} to formula">
                        <span class="dashicons ${icon}"></span>
                        <span class="cfb-field-name">${field.label}</span>
                        <span class="cfb-field-type">${field.type}</span>
                    </button>
                `;
            }).join('');
        }

        renderVariableButtons() {
            // Variables will be loaded via AJAX
            return `
                <div class="cfb-variables-loading">
                    <span class="dashicons dashicons-update-alt cfb-spin"></span>
                    Loading variables...
                </div>
                <div class="cfb-variables-list" style="display: none;"></div>
                <div class="cfb-no-variables" style="display: none;">
                    <p>No variables available. <a href="${this.options.variablesUrl || '#'}" target="_blank">Create variables</a> to use in formulas.</p>
                </div>
            `;
        }

        renderFunctionButtons() {
            const functions = [
                { name: 'ceil', desc: 'Round up', example: 'ceil(4.2) = 5' },
                { name: 'floor', desc: 'Round down', example: 'floor(4.8) = 4' },
                { name: 'round', desc: 'Round nearest', example: 'round(4.5) = 5' },
                { name: 'min', desc: 'Minimum value', example: 'min(5,3,8) = 3' },
                { name: 'max', desc: 'Maximum value', example: 'max(5,3,8) = 8' },
                { name: 'abs', desc: 'Absolute value', example: 'abs(-5) = 5' },
                { name: 'pow', desc: 'Power', example: 'pow(2,3) = 8' },
                { name: 'sqrt', desc: 'Square root', example: 'sqrt(16) = 4' },
                { name: 'if', desc: 'Conditional', example: 'if(x>10, 100, 50)' }
            ];

            return functions.map(func => `
                <button type="button" class="cfb-function-btn" data-function="${func.name}" title="${func.desc}: ${func.example}">
                    <span class="cfb-func-name">${func.name}()</span>
                    <span class="cfb-func-desc">${func.desc}</span>
                </button>
            `).join('');
        }

        renderOperatorButtons() {
            const operators = [
                { symbol: '+', name: 'Add' },
                { symbol: '-', name: 'Subtract' },
                { symbol: '*', name: 'Multiply' },
                { symbol: '/', name: 'Divide' },
                { symbol: '(', name: 'Open parenthesis' },
                { symbol: ')', name: 'Close parenthesis' },
                { symbol: '>', name: 'Greater than' },
                { symbol: '<', name: 'Less than' },
                { symbol: '==', name: 'Equals' },
                { symbol: '!=', name: 'Not equals' }
            ];

            return operators.map(op => `
                <button type="button" class="cfb-operator-btn" data-operator="${op.symbol}" title="${op.name}">
                    ${op.symbol}
                </button>
            `).join('');
        }

        renderCalculationFields() {
            return `
                <div class="cfb-tool-section">
                    <h5>
                        <span class="dashicons dashicons-chart-area"></span>
                        Calculation Fields
                    </h5>
                    <div class="cfb-calc-fields-grid">
                        <p class="cfb-calc-note">
                            <span class="dashicons dashicons-info"></span>
                            You can reference other calculation fields in this total formula.
                        </p>
                        <div id="cfb-calc-fields-list">
                            <!-- Will be populated dynamically -->
                        </div>
                    </div>
                </div>
            `;
        }

        renderHelpContent() {
            return `
                <div class="cfb-help-grid">
                    <div class="cfb-help-section">
                        <h6>Basic Examples</h6>
                        <ul>
                            <li><code>{quantity} * {price}</code> - Simple multiplication</li>
                            <li><code>{length} * {width}</code> - Area calculation</li>
                            <li><code>{base_cost} + {extras}</code> - Add costs</li>
                        </ul>
                    </div>
                    
                    <div class="cfb-help-section">
                        <h6>Advanced Examples</h6>
                        <ul>
                            <li><code>if({quantity} > 10, {price} * 0.9, {price})</code> - Bulk discount</li>
                            <li><code>ceil({total} * 1.1)</code> - Add 10% and round up</li>
                            <li><code>max({calculated}, 50)</code> - Minimum charge</li>
                        </ul>
                    </div>
                    
                    <div class="cfb-help-section">
                        <h6>Field Types</h6>
                        <ul>
                            <li><strong>Number/Slider:</strong> Use directly in calculations</li>
                            <li><strong>Dropdown/Radio:</strong> Uses the price value</li>
                            <li><strong>Checkbox:</strong> Sum of selected prices</li>
                            <li><strong>Text:</strong> Character/word count (if enabled)</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        bindEvents() {
            // Field buttons
            this.container.on('click', '.cfb-field-btn', (e) => {
                const fieldName = $(e.currentTarget).data('field');
                this.insertAtCursor(`{${fieldName}}`);
            });

            // Variable buttons
            this.container.on('click', '.cfb-variable-btn', (e) => {
                const variableName = $(e.currentTarget).data('variable');
                this.insertAtCursor(`{${variableName}}`);
            });

            // Function buttons
            this.container.on('click', '.cfb-function-btn', (e) => {
                const funcName = $(e.currentTarget).data('function');
                this.insertAtCursor(`${funcName}(`);
            });

            // Operator buttons
            this.container.on('click', '.cfb-operator-btn', (e) => {
                const operator = $(e.currentTarget).data('operator');
                this.insertOperator(operator);
            });

            // Validate formula
            this.container.on('click', '.cfb-validate-formula', () => {
                this.validateFormula();
            });

            // Clear formula
            this.container.on('click', '.cfb-clear-formula', () => {
                this.textarea.val('').trigger('input');
                this.showValidation('', 'info');
            });

            // Real-time validation
            this.textarea.on('input', () => {
                this.validateFormula();
            });

            // Auto-resize textarea
            this.textarea.on('input', () => {
                this.autoResize();
            });

            // Load variables
            this.loadVariables();
        }

        loadVariables() {
            const variablesContainer = this.container.find('.cfb-variables-grid');
            const loadingDiv = variablesContainer.find('.cfb-variables-loading');
            const listDiv = variablesContainer.find('.cfb-variables-list');
            const noVariablesDiv = variablesContainer.find('.cfb-no-variables');

            // Check if AJAX variables are available
            if (typeof cfb_admin_ajax === 'undefined') {
                console.error('CFB: cfb_admin_ajax not defined');
                loadingDiv.hide();
                noVariablesDiv.html('<p>Error: Admin AJAX not configured</p>').show();
                return;
            }

            console.log('CFB: Loading variables via AJAX...');

            // AJAX call to get variables
            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_get_variables',
                    nonce: cfb_admin_ajax.nonce
                },
                success: (response) => {
                    console.log('CFB: Variables AJAX response:', response);
                    loadingDiv.hide();

                    if (response.success && response.data && response.data.length > 0) {
                        console.log('CFB: Found', response.data.length, 'variables');
                        const variableButtons = response.data.map(variable => `
                            <button type="button" class="cfb-variable-btn" data-variable="${variable.name}" title="Add ${variable.label} (${variable.value}) to formula">
                                <span class="dashicons dashicons-admin-settings" style="color: ${variable.color || '#4caf50'}"></span>
                                <span class="cfb-variable-name">${variable.label}</span>
                                <span class="cfb-variable-value">${variable.value}</span>
                            </button>
                        `).join('');

                        listDiv.html(variableButtons).show();
                    } else {
                        console.log('CFB: No variables found or response failed');
                        noVariablesDiv.show();
                    }
                },
                error: (xhr, status, error) => {
                    console.error('CFB: Variables AJAX error:', xhr.responseText, status, error);
                    loadingDiv.hide();
                    noVariablesDiv.html('<p>Error loading variables. Check console for details.</p>').show();
                }
            });
        }

        insertAtCursor(text) {
            const textarea = this.textarea[0];
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const value = textarea.value;

            const newValue = value.substring(0, start) + text + value.substring(end);
            textarea.value = newValue;

            // Set cursor position after inserted text
            const newCursorPos = start + text.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);

            this.textarea.trigger('input');
            textarea.focus();
        }

        insertOperator(operator) {
            // Handle parentheses correctly for RTL/LTR
            let textToInsert = operator;

            // Check if this is a parenthesis and handle RTL correctly
            if (operator === '(' || operator === ')') {
                // Always insert the actual operator clicked, regardless of RTL
                // The issue was that RTL was reversing the visual display but not the actual character
                textToInsert = operator;
            }

            // Add spaces around operators (except parentheses)
            if (operator === '(' || operator === ')') {
                this.insertAtCursor(textToInsert);
            } else {
                this.insertAtCursor(` ${textToInsert} `);
            }
        }

        validateFormula() {
            const formula = this.textarea.val().trim();
            
            if (!formula) {
                this.showValidation('Enter a formula to validate', 'info');
                return;
            }
            
            const errors = this.checkSyntaxErrors(formula);
            
            if (errors.length > 0) {
                this.showValidation('Errors: ' + errors.join(', '), 'error');
            } else {
                this.showValidation('Formula syntax is valid ✓', 'success');
            }
        }

        checkSyntaxErrors(formula) {
            const errors = [];
            
            // Check balanced parentheses
            let parenCount = 0;
            for (let char of formula) {
                if (char === '(') parenCount++;
                if (char === ')') parenCount--;
                if (parenCount < 0) {
                    errors.push('Unmatched closing parenthesis');
                    break;
                }
            }
            if (parenCount > 0) {
                errors.push('Unmatched opening parenthesis');
            }
            
            // Check for empty field references
            if (formula.includes('{}')) {
                errors.push('Empty field references found');
            }
            
            // Check for unknown field references
            const fieldRefs = formula.match(/\{([^}]+)\}/g);
            if (fieldRefs) {
                const availableFields = this.options.fields.map(f => f.name);
                fieldRefs.forEach(ref => {
                    const fieldName = ref.slice(1, -1);
                    if (!availableFields.includes(fieldName)) {
                        errors.push(`Unknown field: ${fieldName}`);
                    }
                });
            }
            
            return errors;
        }

        showValidation(message, type) {
            this.validation.removeClass('cfb-validation-success cfb-validation-error cfb-validation-info');
            
            if (type) {
                this.validation.addClass(`cfb-validation-${type}`);
            }
            
            this.validation.text(message);
        }

        autoResize() {
            const textarea = this.textarea[0];
            textarea.style.height = 'auto';
            textarea.style.height = Math.max(60, textarea.scrollHeight) + 'px';
        }

        getFieldIcon(type) {
            const icons = {
                'text': 'dashicons-edit',
                'number': 'dashicons-calculator',
                'slider': 'dashicons-leftright',
                'dropdown': 'dashicons-arrow-down-alt2',
                'radio': 'dashicons-marker',
                'checkbox': 'dashicons-yes',
                'calculation': 'dashicons-chart-line',
                'total': 'dashicons-money-alt'
            };
            return icons[type] || 'dashicons-admin-generic';
        }

        updateFields(fields) {
            this.options.fields = fields;
            this.container.find('.cfb-fields-grid').html(this.renderFieldButtons());
        }

        getValue() {
            return this.textarea.val();
        }

        setValue(value) {
            this.textarea.val(value).trigger('input');
        }
    }

    // Make it globally available
    window.CFBFieldFormulaBuilder = CFBFieldFormulaBuilder;

    // jQuery plugin
    $.fn.cfbFieldFormulaBuilder = function(options) {
        return this.each(function() {
            if (!$(this).data('cfb-field-formula-builder')) {
                $(this).data('cfb-field-formula-builder', new CFBFieldFormulaBuilder(this, options));
            }
        });
    };

})(jQuery);
